services:

  # Local services are available on their ports, but also available on:
  # http://api.localhost.tiangolo.com: backend
  # http://dashboard.localhost.tiangolo.com: frontend
  # etc. To enable it, update .env, set:
  # DOMAIN=localhost.tiangolo.com
  proxy:
    image: traefik:3.0
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    ports:
      - "80:80"
      - "8090:8080"
    # Duplicate the command from docker-compose.yml to add --api.insecure=true
    command:
      # Enable Docker in Traefik, so that it reads labels from Docker services
      - --providers.docker
      # Add a constraint to only use services with the label for this stack
      - --providers.docker.constraints=Label(`traefik.constraint-label`, `traefik-public`)
      # Do not expose all Docker services, only the ones explicitly exposed
      - --providers.docker.exposedbydefault=false
      # Create an entrypoint "http" listening on port 80
      - --entrypoints.http.address=:80
      # Create an entrypoint "https" listening on port 443
      - --entrypoints.https.address=:443
      # Enable the access log, with HTTP requests
      - --accesslog
      # Enable the Traefik log, for configurations and errors
      - --log
      # Enable debug logging for local development
      - --log.level=DEBUG
      # Enable the Dashboard and API
      - --api
      # Enable the Dashboard and API in insecure mode for local development
      - --api.insecure=true
    labels:
      # Enable Traefik for this service, to make it available in the public network
      - traefik.enable=true
      - traefik.constraint-label=traefik-public
      # Dummy https-redirect middleware that doesn't really redirect, only to
      # allow running it locally
      - traefik.http.middlewares.https-redirect.contenttype.autodetect=false
    networks:
      - traefik-public
      - default

  db:
    restart: "no"
    ports:
      - "5432:5432"

  adminer:
    restart: "no"
    ports:
      - "8080:8080"

  backend:
    restart: "no"
    ports:
      - "8000:8000"
    build:
      context: .
      dockerfile: ./backend/Dockerfile
    # command: sleep infinity  # Infinite loop to keep container alive doing nothing
    command:
      - fastapi
      - run
      - --reload
      - "app/main.py"
    develop:
      watch:
        - path: ./backend
          action: sync
          target: /app
          ignore:
            - ./backend/.venv
            - .venv
        - path: ./backend/pyproject.toml
          action: rebuild
    # TODO: remove once coverage is done locally
    volumes:
      - ./backend/htmlcov:/app/htmlcov
    environment:
      SMTP_HOST: "mailcatcher"
      SMTP_PORT: "1025"
      SMTP_TLS: "false"
      EMAILS_FROM_EMAIL: "<EMAIL>"

  mailcatcher:
    image: schickling/mailcatcher
    ports:
      - "1080:1080"
      - "1025:1025"

  frontend:
    restart: "no"
    ports:
      - "5173:80"
    build:
      context: ./frontend
      args:
        - VITE_API_URL=http://localhost:8000
        - NODE_ENV=development

  playwright:
    build:
      context: ./frontend
      dockerfile: Dockerfile.playwright
      args:
        - VITE_API_URL=http://backend:8000
        - NODE_ENV=production
    ipc: host
    depends_on:
      - backend
      - mailcatcher
    env_file:
      - .env
    environment:
      - VITE_API_URL=http://backend:8000
      - MAILCATCHER_HOST=http://mailcatcher:1080
      # For the reports when run locally
      - PLAYWRIGHT_HTML_HOST=0.0.0.0
      - CI=${CI}
    volumes:
      - ./frontend/blob-report:/app/blob-report
      - ./frontend/test-results:/app/test-results
    ports:
      - 9323:9323

networks:
  traefik-public:
    # For local dev, don't expect an external Traefik network
    external: false
